import requests
import datetime
import time
import hashlib
from web3 import Web3
from config import (
    SEPOLIA_RPC_URL, 
    DATAHUB_CONTRACT_ADDRESS, 
    BASESCAN_API_KEY,
    PATIENT_ADDRESS,
    DOCTOR_ADDRESS,
    BUYER_ADDRESS,
    HOSPITAL_ADDRESS
)

# Initialize Web3 with BASE Sepolia RPC
w3 = Web3(Web3.HTTPProvider(SEPOLIA_RPC_URL))

def fetch_contract_transactions():
    """Fetch recent transactions for the DataHub contract from Basescan

    Returns:
        list: Recent transactions involving the DataHub contract
    """
    try:
        # Get transactions to/from the contract
        contract_address = DATAHUB_CONTRACT_ADDRESS

        # We'll simulate fetching from Basescan API (in a real implementation, you would use their API)
        # For demo purposes, we'll return mock data that resembles real transactions
        transactions = [
            {
                "hash": "0xc3df3885a00b773b549c3164f2984f943bab09d3ddfd28b65141a910efbbc566",
                "from": PATIENT_ADDRESS,
                "to": contract_address,
                "value": "0",
                "function": "Contract Creation",
                "timestamp": int(datetime.datetime.now().timestamp()) - 86400 * 7,  # 7 days ago
                "gas_used": 1500000,
                "gas_price": 0.1,  # Gwei
                "status": "Success"
            },
            {
                "hash": "0x8a7d2e13b0d2e8b65f9d5f38b6b1a67980c89d9c6c8b8a7e4f0a7f9c7e8d6b5a",
                "from": DOCTOR_ADDRESS,
                "to": contract_address,
                "value": "0",
                "function": "storeData",
                "timestamp": int(datetime.datetime.now().timestamp()) - 86400 * 3,  # 3 days ago
                "gas_used": 120000,
                "gas_price": 0.15,  # Gwei
                "status": "Success"
            },
            {
                "hash": "0x7b6c4e8d5f3a2b1c0d9e8f7a6b5c4d3e2f1a0b9c8d7e6f5a4b3c2d1e0f9a8b7c6",
                "from": BUYER_ADDRESS,
                "to": contract_address,
                "value": "100000000000000000",  # 0.1 ETH
                "function": "request",
                "timestamp": int(datetime.datetime.now().timestamp()) - 86400 * 2,  # 2 days ago
                "gas_used": 80000,
                "gas_price": 0.12,  # Gwei
                "status": "Success"
            },
            {
                "hash": "0x6a5b4c3d2e1f0a9b8c7d6e5f4a3b2c1d0e9f8a7b6c5d4e3f2a1b0c9d8e7f6a5b4",
                "from": HOSPITAL_ADDRESS,
                "to": contract_address,
                "value": "0",
                "function": "reply",
                "timestamp": int(datetime.datetime.now().timestamp()) - 86400 * 1,  # 1 day ago
                "gas_used": 95000,
                "gas_price": 0.14,  # Gwei
                "status": "Success"
            },
            {
                "hash": "0x5a4b3c2d1e0f9a8b7c6d5e4f3a2b1c0d9e8f7a6b5c4d3e2f1a0b9c8d7e6f5a4b3",
                "from": BUYER_ADDRESS,
                "to": contract_address,
                "value": "0",
                "function": "finalize",
                "timestamp": int(datetime.datetime.now().timestamp()) - 3600 * 12,  # 12 hours ago
                "gas_used": 110000,
                "gas_price": 0.13,  # Gwei
                "status": "Success"
            },
            {
                "hash": "0x7000eb21d74687aeadf10aae5ab76c9ccf5355269df5c1ed3da4a24338a11a4c",
                "from": DOCTOR_ADDRESS,
                "to": contract_address,
                "value": "0",
                "function": "storeData",
                "timestamp": int(datetime.datetime.now().timestamp()) - 3600 * 2,  # 2 hours ago
                "gas_used": 1612967,
                "gas_price": 0.002,  # Gwei
                "status": "Success"
            }
        ]

        # Calculate gas fee in ETH for each transaction
        for tx in transactions:
            tx["gas_fee"] = (tx["gas_used"] * tx["gas_price"] * 1e-9)  # Convert to ETH

        return transactions
    except Exception as e:
        print(f"Error fetching contract transactions: {str(e)}")
        return []

def fetch_wallet_balance_from_basescan(wallet_address):
    """Fetch wallet balance from Sepolia Basescan API

    Args:
        wallet_address (str): The wallet address to check

    Returns:
        float: Balance in ETH, or None if the API call fails
    """
    try:
        # Sepolia Basescan API endpoint for account balance
        # According to https://docs.basescan.org/getting-started/endpoint-urls
        url = f"https://api-sepolia.basescan.org/api?module=account&action=balance&address={wallet_address}&tag=latest&apikey={BASESCAN_API_KEY}"

        print(f"Fetching balance from Sepolia Basescan API: {url}")

        # Make the request
        response = requests.get(url, timeout=10)

        # Check if the request was successful
        if response.status_code == 200:
            data = response.json()
            print(f"Basescan API response: {data}")

            # Check for success in the response
            if data.get("status") == "1" and "result" in data:
                # Convert string to int and then to ETH
                balance_wei = int(data["result"])
                balance_eth = w3.from_wei(balance_wei, 'ether')
                print(f"Got balance for {wallet_address} from Sepolia Basescan: {balance_wei} wei ({balance_eth} ETH)")
                return float(balance_eth)
            else:
                error_msg = data.get('message', 'Unknown error')
                print(f"Sepolia Basescan API error: {error_msg}")
                print(f"Response: {data}")

                # Try fallback to BASE Sepolia RPC
                print("Falling back to BASE Sepolia RPC for wallet balance")
                return fetch_wallet_balance_from_sepolia(wallet_address)
        else:
            print(f"Sepolia Basescan API HTTP error: {response.status_code}, Response: {response.text}")

            # Try fallback to BASE Sepolia RPC
            print("Falling back to BASE Sepolia RPC for wallet balance due to HTTP error")
            return fetch_wallet_balance_from_sepolia(wallet_address)
    except Exception as e:
        print(f"Error getting wallet balance from Sepolia Basescan: {str(e)}")
        return fetch_wallet_balance_from_sepolia(wallet_address)

def fetch_wallet_balance_from_sepolia(wallet_address):
    """Fetch wallet balance from BASE Sepolia RPC

    Args:
        wallet_address (str): The wallet address to check

    Returns:
        float: Balance in ETH, or None if the RPC call fails
    """
    try:
        # Check if the address is valid
        if not w3.is_address(wallet_address):
            print(f"Invalid wallet address: {wallet_address}")
            return None

        # Get balance in wei
        balance_wei = w3.eth.get_balance(wallet_address)
        
        # Convert to ETH
        balance_eth = w3.from_wei(balance_wei, 'ether')
        
        print(f"Got balance for {wallet_address} from BASE Sepolia RPC: {balance_wei} wei ({balance_eth} ETH)")
        return float(balance_eth)
    except Exception as e:
        print(f"Error getting wallet balance from BASE Sepolia RPC: {str(e)}")
        return None

def fetch_sepolia_gas_prices():
    """Fetch current gas prices from BASE Sepolia

    Returns:
        dict: Gas prices in Gwei for different transaction types
    """
    try:
        # Get gas price from the network
        gas_price_wei = w3.eth.gas_price
        gas_price_gwei = w3.from_wei(gas_price_wei, 'gwei')
        
        # Estimate different gas prices based on current network conditions
        # In a real implementation, you might want to use a gas price oracle
        base_gas_price = float(gas_price_gwei)
        
        return {
            "slow": max(0.001, base_gas_price * 0.8),  # 20% below current
            "standard": base_gas_price,
            "fast": base_gas_price * 1.2,  # 20% above current
            "instant": base_gas_price * 1.5,  # 50% above current
            "current_wei": gas_price_wei,
            "current_gwei": float(gas_price_gwei)
        }
    except Exception as e:
        print(f"Error fetching gas prices: {str(e)}")
        # Return fallback values
        return {
            "slow": 0.001,
            "standard": 0.002,
            "fast": 0.003,
            "instant": 0.005,
            "current_wei": 2000000000,  # 2 Gwei in wei
            "current_gwei": 2.0
        }

def load_contract_abi():
    """Load the DataHub contract ABI

    Returns:
        list: Contract ABI as a list of dictionaries
    """
    try:
        # In a real implementation, you would load this from a file
        # For now, we'll return a minimal ABI with the functions we need
        abi = [
            {
                "inputs": [
                    {
                        "internalType": "string",
                        "name": "cid",
                        "type": "string"
                    },
                    {
                        "internalType": "string",
                        "name": "metadata",
                        "type": "string"
                    }
                ],
                "name": "storeData",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {
                        "internalType": "uint256",
                        "name": "amount",
                        "type": "uint256"
                    },
                    {
                        "internalType": "string",
                        "name": "template",
                        "type": "string"
                    }
                ],
                "name": "request",
                "outputs": [
                    {
                        "internalType": "uint256",
                        "name": "",
                        "type": "uint256"
                    }
                ],
                "stateMutability": "payable",
                "type": "function"
            },
            {
                "inputs": [
                    {
                        "internalType": "uint256",
                        "name": "requestId",
                        "type": "uint256"
                    },
                    {
                        "internalType": "string[]",
                        "name": "cids",
                        "type": "string[]"
                    }
                ],
                "name": "reply",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {
                        "internalType": "uint256",
                        "name": "requestId",
                        "type": "uint256"
                    },
                    {
                        "internalType": "bool",
                        "name": "approved",
                        "type": "bool"
                    },
                    {
                        "internalType": "address[]",
                        "name": "recipients",
                        "type": "address[]"
                    }
                ],
                "name": "finalize",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]
        return abi
    except Exception as e:
        print(f"Error loading contract ABI: {str(e)}")
        return []

def track_transaction(tx_hash, operation, wallet_address, gas_used=None, gas_price=None):
    """Track a transaction in the session state

    Args:
        tx_hash (str): Transaction hash
        operation (str): Operation type
        wallet_address (str): Wallet address
        gas_used (int, optional): Gas used
        gas_price (float, optional): Gas price in Gwei
    """
    try:
        # Calculate gas fee if both gas_used and gas_price are provided
        gas_fee = None
        if gas_used and gas_price:
            gas_fee = (gas_used * gas_price * 1e-9)  # Convert to ETH
        
        # Create transaction record
        transaction = {
            "id": f"tx-{int(time.time())}",
            "hash": tx_hash,
            "operation": operation,
            "wallet_address": wallet_address,
            "timestamp": int(time.time()),
            "gas_used": gas_used,
            "gas_price": gas_price,
            "gas_fee": gas_fee,
            "status": "Completed"
        }
        
        # Add to session state if it exists
        import streamlit as st
        if "transaction_history" not in st.session_state:
            st.session_state.transaction_history = []
        
        st.session_state.transaction_history.append(transaction)
        
        # Also log to file
        with open("transaction_log.txt", "a") as f:
            f.write(f"{datetime.datetime.now().isoformat()}: {operation} - {tx_hash} - {wallet_address}\n")
            
    except Exception as e:
        print(f"Error tracking transaction: {str(e)}") 