import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API endpoint (for backward compatibility)
API_URL = os.getenv("API_URL", "http://127.0.0.1:8000")

# BASE Sepolia testnet connection
SEPOLIA_RPC_URL = os.getenv("SEPOLIA_RPC_URL", "https://base-sepolia-rpc.publicnode.com")
CONTRACT_ADDRESS = os.getenv("CONTRACT_ADDRESS", "******************************************")

# Role-specific addresses from environment variables
WALLET_ADDRESS = os.getenv("WALLET_ADDRESS")
PATIENT_ADDRESS = os.getenv("PATIENT_ADDRESS")
DOCTOR_ADDRESS = os.getenv("DOCTOR_ADDRESS")
HOSPITAL_ADDRESS = os.getenv("HOSPITAL_ADDRESS")
BUYER_ADDRESS = os.getenv("BUYER_ADDRESS")
GROUP_MANAGER_ADDRESS = os.getenv("GROUP_MANAGER_ADDRESS")
REVOCATION_MANAGER_ADDRESS = os.getenv("REVOCATION_MANAGER_ADDRESS")

# DataHub contract address on BASE Sepolia
DATAHUB_CONTRACT_ADDRESS = os.getenv("DATAHUB_CONTRACT_ADDRESS", "******************************************")

# Basescan API key
BASESCAN_API_KEY = os.getenv("BASESCAN_API_KEY", "I61T8UZK7YKRC8P61BHF6237PG9GC2VK3Y")

# Test accounts for demo purposes
TEST_ACCOUNTS = {
    "Patient": {
        "address": PATIENT_ADDRESS or "******************************************",
        "balance": 0.5,
        "role": "Patient"
    },
    "Doctor": {
        "address": DOCTOR_ADDRESS or "******************************************",
        "balance": 1.2,
        "role": "Doctor"
    },
    "Hospital": {
        "address": HOSPITAL_ADDRESS or "******************************************",
        "balance": 5.0,
        "role": "Hospital"
    },
    "Buyer": {
        "address": BUYER_ADDRESS or "0x3Fa2c09c14453c7acaC39E3fd57e0c6F1da3f5ce",
        "balance": 10.0,
        "role": "Buyer"
    },
    "Group Manager": {
        "address": GROUP_MANAGER_ADDRESS or "******************************************",
        "balance": 2.0,
        "role": "Group Manager"
    },
    "Revocation Manager": {
        "address": REVOCATION_MANAGER_ADDRESS or "******************************************",
        "balance": 1.5,
        "role": "Revocation Manager"
    }
}

# Page configuration
PAGE_CONFIG = {
    "page_title": "Healthcare Data Sharing",
    "page_icon": "🏥",
    "layout": "wide"
} 