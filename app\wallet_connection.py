import streamlit as st
import requests
from web3 import Web3
from config import TEST_ACCOUNTS, SEPOLIA_RPC_URL
from blockchain_utils import fetch_wallet_balance_from_basescan

# Initialize Web3 with BASE Sepolia RPC
w3 = Web3(Web3.HTTPProvider(SEPOLIA_RPC_URL))

def connect_wallet():
    """Connect wallet and authenticate user"""
    # In a real app, this would use Wallet Connect or similar
    # For demo purposes, we'll simulate a connection

    # Get the selected account from session state
    selected_account = st.session_state.get("selected_account")

    # If no account is selected, use the first account in the list
    if not selected_account or selected_account not in TEST_ACCOUNTS:
        selected_account = list(TEST_ACCOUNTS.keys())[0]
        st.session_state.selected_account = selected_account

    # Get the account info from the TEST_ACCOUNTS dictionary
    account_info = TEST_ACCOUNTS[selected_account]
    wallet_address = account_info["address"]
    private_key = account_info.get("private_key", "0x" + "0" * 64)  # Default private key

    # Generate an authentication challenge
    try:
        from api_client import api_client
        challenge_result = api_client.get_auth_challenge(wallet_address)

        if challenge_result and challenge_result.get("success"):
            challenge_data = challenge_result.get("data", {})
            challenge = challenge_data.get("challenge")

            # In a real app, this would be signed by the wallet
            # For demo purposes, we'll simulate signing with the private key
            message_hash = Web3.keccak(text=challenge)
            signed_message = w3.eth.account.sign_message(
                message_hash,
                private_key=private_key
            )
            signature = signed_message.signature.hex()

            # Verify the signature with the backend
            verify_result = api_client.verify_auth(wallet_address, signature)

            if verify_result and verify_result.get("success"):
                auth_data = verify_result.get("data", {})
                if auth_data.get("authenticated", False):
                    # Authentication successful
                    st.session_state.wallet_connected = True
                    st.session_state.wallet_address = wallet_address
                    st.session_state.private_key = private_key

                    # Set the role from the authentication response
                    st.session_state.selected_role = auth_data.get("role", account_info["role"])

                    # Debug info
                    print(f"Connected as {selected_account} with role {st.session_state.selected_role}")

                    # Set a flag to trigger rerun
                    st.session_state.trigger_rerun = True
                else:
                    st.error(f"Authentication failed: {auth_data.get('message', 'Unknown error')}")
            else:
                st.error(f"Error verifying signature: {verify_result.get('detail', 'Unknown error')}")
        else:
            st.error(f"Error getting authentication challenge: {challenge_result.get('detail', 'Unknown error')}")
    except Exception as e:
        # Fallback to the old method if the authentication API is not available
        print(f"Error connecting to authentication API: {str(e)}")
        print("Falling back to direct connection without authentication")

        st.session_state.wallet_connected = True
        st.session_state.wallet_address = wallet_address
        st.session_state.private_key = private_key

        # Set the initial role based on the account type
        st.session_state.selected_role = account_info["role"]

        # Debug info
        print(f"Connected as {selected_account} with role {st.session_state.selected_role} (fallback)")

        # Set a flag to trigger rerun
        st.session_state.trigger_rerun = True

def render_wallet_sidebar():
    """Render the wallet connection sidebar"""
    with st.sidebar:
        st.title("Healthcare Data Sharing")

        if not st.session_state.wallet_connected:
            # Role selection dropdown with descriptions
            st.subheader("Select Your Role")

            role_descriptions = {
                "Patient": "Access and share your medical records",
                "Doctor": "Create and sign medical records for patients",
                "Hospital": "Manage data purchase requests and group membership",
                "Buyer": "Request to purchase anonymized medical data",
                "Group Manager": "Manage doctor group membership and signature tracing",
                "Revocation Manager": "Assist in signature tracing and revocation"
            }

            selected_role = st.selectbox(
                "Role",
                list(role_descriptions.keys()),
                index=0,
                help="Select a role to connect with"
            )

            # Display role description
            st.caption(role_descriptions[selected_role])

            # Find the account that matches the selected role
            selected_account = None
            for account_name, account_info in TEST_ACCOUNTS.items():
                if account_info["role"] == selected_role:
                    selected_account = account_name
                    break

            if selected_account:
                # Display account details in a nice format
                with st.container():
                    st.markdown("**Account Details:**")
                    col1, col2 = st.columns(2)
                    with col1:
                        st.markdown(f"**Name:** {selected_account}")
                        st.markdown(f"**Role:** {TEST_ACCOUNTS[selected_account]['role']}")
                    with col2:
                        address = TEST_ACCOUNTS[selected_account]['address']
                        st.markdown(f"**Address:** `{address[:6]}...{address[-4:]}`")

                    # Add a small explanation about the account
                    if selected_role == "Patient":
                        st.info("This account will allow you to access and share your medical records.")
                    elif selected_role == "Doctor":
                        st.info("This account will allow you to create and sign medical records for patients.")
                    elif selected_role == "Hospital":
                        st.info("This account will allow you to manage data purchase requests and group membership.")
                    elif selected_role == "Buyer":
                        st.info("This account will allow you to request to purchase anonymized medical data.")
                    elif selected_role == "Group Manager":
                        st.info("This account will allow you to manage doctor group membership and signature tracing.")
                    elif selected_role == "Revocation Manager":
                        st.info("This account will allow you to assist in signature tracing and revocation.")
            else:
                st.error(f"No account found for role: {selected_role}")
                selected_account = list(TEST_ACCOUNTS.keys())[0]  # Fallback to first account

            # Store the selected account in session state
            st.session_state.selected_account = selected_account

            # Connect button
            st.button("Connect Wallet", on_click=connect_wallet)
        else:
            # Display connected wallet info
            st.subheader("Connected Wallet")
            
            # Get account name from address
            account_name = "Unknown"
            for name, info in TEST_ACCOUNTS.items():
                if info["address"].lower() == st.session_state.wallet_address.lower():
                    account_name = name
                    break
            
            st.markdown(f"**Account:** {account_name}")
            st.markdown(f"**Role:** {st.session_state.selected_role}")
            st.markdown(f"**Address:** `{st.session_state.wallet_address[:6]}...{st.session_state.wallet_address[-4:]}`")
            
            # Fetch and display balance
            balance = fetch_wallet_balance_from_basescan(st.session_state.wallet_address)
            if balance is not None:
                st.metric("Balance", f"{balance:.4f} ETH")
            else:
                st.metric("Balance", "N/A")
            
            # Disconnect button
            if st.button("Disconnect"):
                st.session_state.wallet_connected = False
                st.session_state.wallet_address = None
                st.session_state.private_key = None
                st.session_state.selected_role = None
                st.session_state.trigger_rerun = True
                st.rerun()

def initialize_session_state():
    """Initialize session state variables"""
    if "wallet_connected" not in st.session_state:
        st.session_state.wallet_connected = False
        st.session_state.wallet_address = None
        st.session_state.private_key = None
        st.session_state.trigger_rerun = False
        st.session_state.records = []

        # Set the default role
        default_role = "Patient"
        st.session_state.selected_role = default_role

        # Find the account that matches the default role
        default_account = None
        for account_name, account_info in TEST_ACCOUNTS.items():
            if account_info["role"] == default_role:
                default_account = account_name
                break

        if default_account:
            st.session_state.selected_account = default_account
        else:
            # Fallback to first account if no match found
            st.session_state.selected_account = list(TEST_ACCOUNTS.keys())[0] 