import streamlit as st
import pandas as pd
import datetime
import time
from blockchain_utils import fetch_contract_transactions, fetch_sepolia_gas_prices, fetch_wallet_balance_from_basescan

def safe_get(d, keys, default=None):
    """Safely get nested dictionary values
    
    Args:
        d (dict): Dictionary to search
        keys (list): List of keys to traverse
        default: Default value if key not found
        
    Returns:
        Value at the nested key location or default
    """
    current = d
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    return current

def render_gas_fees_tab(wallet_address):
    """Render the gas fees tab with transaction history and gas analysis
    
    Args:
        wallet_address (str): The wallet address to analyze
    """
    st.header("Gas Fees Analysis")
    
    # Fetch current gas prices
    gas_prices = fetch_sepolia_gas_prices()
    
    # Display current gas prices
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Slow", f"{gas_prices['slow']:.3f} Gwei")
    with col2:
        st.metric("Standard", f"{gas_prices['standard']:.3f} Gwei")
    with col3:
        st.metric("Fast", f"{gas_prices['fast']:.3f} Gwei")
    with col4:
        st.metric("Instant", f"{gas_prices['instant']:.3f} Gwei")
    
    # Fetch contract transactions
    transactions = fetch_contract_transactions()
    
    if transactions:
        # Convert to DataFrame for analysis
        df = pd.DataFrame(transactions)
        
        # Add date column
        df['date'] = pd.to_datetime(df['timestamp'], unit='s')
        
        # Calculate additional metrics
        df['gas_fee_eth'] = df['gas_used'] * df['gas_price'] * 1e-9
        
        # Display transaction history
        st.subheader("Recent Contract Transactions")
        
        # Filter options
        col1, col2 = st.columns(2)
        with col1:
            time_filter = st.selectbox(
                "Time Period",
                ["All Time", "Last 24 Hours", "Last 7 Days", "Last 30 Days"]
            )
        
        with col2:
            function_filter = st.selectbox(
                "Function",
                ["All Functions"] + list(df['function'].unique())
            )
        
        # Apply filters
        filtered_df = df.copy()
        
        if time_filter != "All Time":
            now = datetime.datetime.now()
            if time_filter == "Last 24 Hours":
                cutoff = now - datetime.timedelta(days=1)
            elif time_filter == "Last 7 Days":
                cutoff = now - datetime.timedelta(days=7)
            elif time_filter == "Last 30 Days":
                cutoff = now - datetime.timedelta(days=30)
            
            filtered_df = filtered_df[filtered_df['date'] >= cutoff]
        
        if function_filter != "All Functions":
            filtered_df = filtered_df[filtered_df['function'] == function_filter]
        
        # Display filtered transactions
        if not filtered_df.empty:
            # Format the display
            display_df = filtered_df[['date', 'function', 'from', 'gas_used', 'gas_price', 'gas_fee_eth', 'status']].copy()
            display_df['from'] = display_df['from'].apply(lambda x: f"{x[:6]}...{x[-4:]}" if len(x) > 10 else x)
            display_df['gas_fee_eth'] = display_df['gas_fee_eth'].apply(lambda x: f"{x:.6f}")
            display_df['gas_price'] = display_df['gas_price'].apply(lambda x: f"{x:.3f}")
            
            st.dataframe(display_df, use_container_width=True)
            
            # Summary statistics
            st.subheader("Gas Fee Statistics")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                total_gas_fees = filtered_df['gas_fee_eth'].sum()
                st.metric("Total Gas Fees", f"{total_gas_fees:.6f} ETH")
            
            with col2:
                avg_gas_fee = filtered_df['gas_fee_eth'].mean()
                st.metric("Average Gas Fee", f"{avg_gas_fee:.6f} ETH")
            
            with col3:
                total_transactions = len(filtered_df)
                st.metric("Total Transactions", total_transactions)
            
            with col4:
                avg_gas_price = filtered_df['gas_price'].mean()
                st.metric("Average Gas Price", f"{avg_gas_price:.3f} Gwei")
            
            # Gas fee trends
            st.subheader("Gas Fee Trends")
            
            # Group by date and calculate daily totals
            daily_fees = filtered_df.groupby(filtered_df['date'].dt.date)['gas_fee_eth'].sum().reset_index()
            daily_fees['date'] = pd.to_datetime(daily_fees['date'])
            
            if len(daily_fees) > 1:
                st.line_chart(daily_fees.set_index('date')['gas_fee_eth'])
            else:
                st.info("Not enough data to show trends")
            
            # Export options
            st.subheader("Export Data")
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("Export to CSV"):
                    csv = generate_gas_fee_csv(filtered_df, "detailed")
                    st.download_button(
                        label="Download CSV",
                        data=csv,
                        file_name=f"gas_fees_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                        mime="text/csv"
                    )
            
            with col2:
                if st.button("Export Summary"):
                    csv = generate_gas_fee_csv(filtered_df, "summary")
                    st.download_button(
                        label="Download Summary CSV",
                        data=csv,
                        file_name=f"gas_fees_summary_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                        mime="text/csv"
                    )
        else:
            st.info("No transactions found for the selected filters")
    else:
        st.info("No transaction data available")

def generate_gas_fee_export(fees_data, detail_level="detailed"):
    """Generate gas fee export data
    
    Args:
        fees_data (DataFrame): Transaction data
        detail_level (str): Level of detail ("detailed" or "summary")
        
    Returns:
        str: CSV formatted data
    """
    if detail_level == "detailed":
        # Export all transaction details
        export_df = fees_data[['date', 'function', 'from', 'to', 'gas_used', 'gas_price', 'gas_fee_eth', 'status']].copy()
        export_df['date'] = export_df['date'].dt.strftime('%Y-%m-%d %H:%M:%S')
        return export_df.to_csv(index=False)
    else:
        # Export summary statistics
        summary_data = {
            'Metric': [
                'Total Transactions',
                'Total Gas Fees (ETH)',
                'Average Gas Fee (ETH)',
                'Average Gas Price (Gwei)',
                'Total Gas Used',
                'Date Range Start',
                'Date Range End'
            ],
            'Value': [
                len(fees_data),
                f"{fees_data['gas_fee_eth'].sum():.6f}",
                f"{fees_data['gas_fee_eth'].mean():.6f}",
                f"{fees_data['gas_price'].mean():.3f}",
                f"{fees_data['gas_used'].sum():,}",
                fees_data['date'].min().strftime('%Y-%m-%d %H:%M:%S'),
                fees_data['date'].max().strftime('%Y-%m-%d %H:%M:%S')
            ]
        }
        summary_df = pd.DataFrame(summary_data)
        return summary_df.to_csv(index=False)

def generate_gas_fee_csv(fees_data, detail_level="detailed"):
    """Generate gas fee CSV data (alias for generate_gas_fee_export)
    
    Args:
        fees_data (DataFrame): Transaction data
        detail_level (str): Level of detail ("detailed" or "summary")
        
    Returns:
        str: CSV formatted data
    """
    return generate_gas_fee_export(fees_data, detail_level) 