import streamlit as st
import datetime
import time
from config import API_URL, TEST_ACCOUNTS
from api_client import api_client

def fetch_patient_records():
    """Fetch all records for the current patient"""
    if not st.session_state.wallet_connected:
        return

    if st.session_state.selected_role != "Patient":
        return

    try:
        # Call new API to get all records for this patient
        result = api_client.list_patient_records(st.session_state.wallet_address)

        if result and result.get("success"):
            records = result.get("data", [])
            if "records" not in st.session_state:
                st.session_state.records = []

            # Add new records to the session state
            for record in records:
                if not any(r.get("cid") == record.get("cid") for r in st.session_state.records):
                    st.session_state.records.append(record)

            print(f"Fetched {len(records)} records for patient {st.session_state.wallet_address}")
        else:
            print(f"Failed to fetch records: {result}")
    except Exception as e:
        print(f"Error fetching patient records: {str(e)}")

def fetch_data_requests():
    """Fetch data requests for this patient"""
    try:
        # Call API to get data requests for this patient
        response = api_client.get_data_requests(st.session_state.wallet_address)

        if response:
            return response
        else:
            print("Error fetching data requests")
            return []
    except Exception as e:
        print(f"Error fetching data requests: {str(e)}")
        return []

def render_patient_dashboard():
    """Render the patient dashboard"""
    st.title("Patient Dashboard")
    
    # Fetch patient records
    fetch_patient_records()
    
    # Tabs for different patient actions
    tab1, tab2, tab3 = st.tabs(["My Records", "Share Records", "Data Requests"])
    
    with tab1:
        st.header("My Medical Records")
        
        # Display records
        if hasattr(st.session_state, 'records') and st.session_state.records:
            for i, record in enumerate(st.session_state.records):
                with st.expander(f"Record {i+1} - {record.get('diagnosis', 'No Diagnosis')}"):
                    # Record details
                    col1, col2 = st.columns(2)
                    with col1:
                        doctor_id = record.get('doctorId', 'N/A')
                        if isinstance(doctor_id, str) and len(doctor_id) > 10:
                            st.markdown(f"**Doctor ID:** {doctor_id[:6]}...{doctor_id[-4:]}")
                        else:
                            st.markdown(f"**Doctor ID:** {doctor_id}")

                    with col2:
                        timestamp = record.get('timestamp', 0)
                        # Convert timestamp to int if it's a string
                        if isinstance(timestamp, str):
                            try:
                                timestamp = int(float(timestamp))
                            except (ValueError, TypeError):
                                timestamp = 0
                        elif not isinstance(timestamp, (int, float)):
                            timestamp = 0
                        
                        date_str = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                        st.markdown(f"**Created:** {date_str}")
                        st.markdown(f"**IPFS CID:** {record.get('cid', 'N/A')}")
                        st.markdown(f"**Hospital:** {record.get('hospitalInfo', 'N/A')}")

                    # Demographics section
                    st.subheader("Demographics")
                    demographics = record.get('demographics', {})
                    if demographics:
                        col1, col2 = st.columns(2)
                        with col1:
                            st.markdown(f"**Age:** {demographics.get('age', 'N/A')}")
                            st.markdown(f"**Gender:** {demographics.get('gender', 'N/A')}")
                        with col2:
                            st.markdown(f"**Location:** {demographics.get('location', 'N/A')}")
                            st.markdown(f"**Ethnicity:** {demographics.get('ethnicity', 'N/A')}")
                    else:
                        st.info("No demographics information available")

                    # Medical data section
                    st.subheader("Medical Data")
                    medical_data = record.get('medical_data', {})
                    if medical_data:
                        st.markdown(f"**Diagnosis:** {medical_data.get('diagnosis', 'N/A')}")
                        st.markdown(f"**Treatment:** {medical_data.get('treatment', 'N/A')}")

                        col1, col2 = st.columns(2)
                        with col1:
                            st.markdown("**Medications:**")
                            st.text_area(f"Medications_{i}", value=medical_data.get('medications', ''), height=100, disabled=True)
                        with col2:
                            st.markdown("**Lab Results:**")
                            st.text_area(f"Lab_Results_{i}", value=medical_data.get('lab_results', ''), height=100, disabled=True)
                    else:
                        st.info("No detailed medical data available")

                    # Notes section
                    st.subheader("Additional Notes")
                    st.text_area(f"notes_{i}", value=record.get('notes', ''), height=100, disabled=True)

                    # Option to share with a doctor
                    if st.button(f"Share with Doctor", key=f"share_{i}"):
                        st.session_state.record_to_share = record
                        st.session_state.trigger_rerun = True
        else:
            st.info("No records found. Records will appear here automatically when a doctor creates them for you.")

    with tab2:
        st.header("Share Records with Doctors")

        # Form for sharing records
        with st.form("share_record_form"):
            record_cid = st.text_input("Record CID to Share")
            doctor_address = st.text_input("Doctor's Wallet Address")

            submit_button = st.form_submit_button("Share Record via IPFS")

            if submit_button:
                # Call API to share record
                try:
                    # The API expects a ShareRequest object and a wallet_address as separate parameters
                    # But FastAPI combines them in a single JSON object
                    response = api_client.share_record(record_cid, doctor_address, st.session_state.wallet_address)

                    # If the first attempt fails, try an alternative format
                    if response.status_code == 404 or response.status_code == 422:
                        print(f"First attempt failed with status {response.status_code}, trying alternative format...")
                        response = api_client.share_record(record_cid, doctor_address, st.session_state.wallet_address)

                    # Print debug info
                    print(f"Share request: {record_cid} with doctor {doctor_address}")
                    print(f"Response status: {response.status_code}")

                    if response.status_code == 200:
                        result = response.json()
                        st.success("Record shared successfully via IPFS!")
                        st.info(f"Sharing Metadata CID: {result['sharing_metadata_cid']}")
                        st.info(f"Shared Record CID: {result['record_cid']}")
                        st.info("The doctor can now access this record using the Sharing Metadata CID.")

                        # Create a shareable link (in a real app, this would be sent to the doctor)
                        sharing_link = f"https://ipfs.io/ipfs/{result['sharing_metadata_cid']}"
                        st.markdown(f"**IPFS Link:** [View Sharing Metadata]({sharing_link})")
                        st.info("Send this Sharing Metadata CID to the doctor through a secure channel.")
                    else:
                        st.error(f"Error: {response.json().get('detail', 'Unknown error')}")
                except Exception as e:
                    st.error(f"Error: {str(e)}")

    with tab3:
        st.header("Data Requests")

        # For demo purposes, let's create some sample data requests
        # In a real implementation, these would come from the API
        if "data_requests" not in st.session_state:
            # Try to fetch from API first
            api_requests = fetch_data_requests()

            if api_requests:
                st.session_state.data_requests = api_requests
            else:
                # Use sample data as fallback
                st.session_state.data_requests = [
                    {
                        "request_id": "req_001",
                        "buyer": "0x3Fa2c09c14453c7acaC39E3fd57e0c6F1da3f5ce",  # Buyer address
                        "hospital": "0x28B317594b44483D24EE8AdCb13A1b148497C6ba",  # Hospital address
                        "template": {
                            "category": "Cardiology",
                            "demographics": {"age": True, "gender": True},
                            "medical_data": {"diagnosis": True, "treatment": True},
                            "time_period": "1 year",
                            "min_records": 1
                        },
                        "status": "pending",
                        "timestamp": int(time.time()) - 3600,  # 1 hour ago
                        "amount": 0.1
                    }
                ]

        # Add a refresh button
        if st.button("Refresh Data Requests"):
            api_requests = fetch_data_requests()
            if api_requests:
                st.session_state.data_requests = api_requests
            st.success("Data requests refreshed!")

        # Display data requests
        if not st.session_state.data_requests:
            st.info("No data requests found. Hospitals will send requests here when buyers request data that matches your records.")
        else:
            st.subheader("Pending Data Requests")

            for i, request in enumerate(st.session_state.data_requests):
                if request.get("status") == "pending":
                    with st.expander(f"Request {request['request_id']} - {request['template']['category']}"):
                        # Request details
                        col1, col2 = st.columns(2)
                        with col1:
                            st.markdown(f"**Request ID:** {request['request_id']}")

                            # Format buyer address
                            buyer = request.get("buyer", "Unknown")
                            buyer_display = buyer
                            if len(buyer) > 10:
                                buyer_display = f"{buyer[:6]}...{buyer[-4:]}"

                            # Check if it's a known address
                            buyer_name = "Unknown"
                            for name, info in TEST_ACCOUNTS.items():
                                if info["address"].lower() == buyer.lower():
                                    buyer_name = name
                                    break

                            st.markdown(f"**Buyer:** {buyer_name} ({buyer_display})")
                            st.markdown(f"**Amount:** {request.get('amount', 0)} ETH")

                        with col2:
                            # Format hospital address
                            hospital = request.get("hospital", "Unknown")
                            hospital_display = hospital
                            if len(hospital) > 10:
                                hospital_display = f"{hospital[:6]}...{hospital[-4:]}"

                            # Check if it's a known address
                            hospital_name = "Unknown"
                            for name, info in TEST_ACCOUNTS.items():
                                if info["address"].lower() == hospital.lower():
                                    hospital_name = name
                                    break

                            st.markdown(f"**Hospital:** {hospital_name} ({hospital_display})")
                            
                            # Format timestamp
                            timestamp = request.get("timestamp", 0)
                            if isinstance(timestamp, (int, float)):
                                date_str = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                                st.markdown(f"**Requested:** {date_str}")

                        # Template details
                        st.subheader("Data Request Template")
                        template = request.get("template", {})
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            st.markdown(f"**Category:** {template.get('category', 'N/A')}")
                            st.markdown(f"**Time Period:** {template.get('time_period', 'N/A')}")
                            st.markdown(f"**Min Records:** {template.get('min_records', 'N/A')}")
                        
                        with col2:
                            st.markdown("**Requested Data:**")
                            demographics = template.get("demographics", {})
                            if demographics:
                                st.markdown("- Demographics: " + ", ".join([k for k, v in demographics.items() if v]))
                            
                            medical_data = template.get("medical_data", {})
                            if medical_data:
                                st.markdown("- Medical Data: " + ", ".join([k for k, v in medical_data.items() if v]))

                        # Action buttons
                        col1, col2 = st.columns(2)
                        with col1:
                            if st.button(f"Approve Request", key=f"approve_{i}"):
                                st.success(f"Request {request['request_id']} approved!")
                                # In a real implementation, this would call the smart contract
                                request["status"] = "approved"
                        with col2:
                            if st.button(f"Reject Request", key=f"reject_{i}"):
                                st.error(f"Request {request['request_id']} rejected!")
                                # In a real implementation, this would call the smart contract
                                request["status"] = "rejected" 