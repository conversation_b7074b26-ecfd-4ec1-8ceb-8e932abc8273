# Refactored Healthcare Data Sharing Application

This document describes the refactored structure of the healthcare data sharing application, which has been broken down from a single large `main.py` file into multiple focused modules.

## File Structure

```
app/
├── main.py                    # Original large file (5,240 lines)
├── main_refactored.py         # New modular main file
├── config.py                  # Configuration and constants
├── blockchain_utils.py        # Blockchain-related functions
├── gas_fees.py               # Gas fee calculations and exports
├── wallet_connection.py      # Wallet connection and authentication
├── patient_dashboard.py      # Patient-specific dashboard functions
├── doctor_dashboard.py       # Doctor-specific dashboard functions
├── hospital_dashboard.py     # Hospital-specific dashboard functions
├── buyer_dashboard.py        # Buyer-specific dashboard functions
├── manager_dashboard.py      # Group/Revocation manager dashboard functions
├── api_client.py             # API client (existing)
└── README_REFACTORED.md      # This file
```

## Module Descriptions

### 1. `config.py`
**Purpose**: Centralized configuration management
**Contents**:
- Environment variables and API endpoints
- Blockchain network configuration
- Test account definitions
- Page configuration settings

**Key Functions**:
- `API_URL`: Backend API endpoint
- `TEST_ACCOUNTS`: Demo account configurations
- `PAGE_CONFIG`: Streamlit page settings

### 2. `blockchain_utils.py`
**Purpose**: Blockchain interaction utilities
**Contents**:
- Web3 connection setup
- Contract transaction fetching
- Wallet balance queries
- Gas price fetching
- Transaction tracking

**Key Functions**:
- `fetch_contract_transactions()`: Get recent contract transactions
- `fetch_wallet_balance_from_basescan()`: Query wallet balance
- `fetch_sepolia_gas_prices()`: Get current gas prices
- `track_transaction()`: Log transaction details

### 3. `gas_fees.py`
**Purpose**: Gas fee analysis and reporting
**Contents**:
- Gas fee calculation utilities
- Transaction history analysis
- Data export functionality
- Gas fee trend visualization

**Key Functions**:
- `render_gas_fees_tab()`: Display gas fee dashboard
- `generate_gas_fee_csv()`: Export gas fee data
- `safe_get()`: Safe dictionary access utility

### 4. `wallet_connection.py`
**Purpose**: Wallet connection and authentication
**Contents**:
- Wallet connection logic
- Authentication flow
- Session state management
- Sidebar rendering

**Key Functions**:
- `connect_wallet()`: Handle wallet connection
- `render_wallet_sidebar()`: Display wallet sidebar
- `initialize_session_state()`: Setup session variables

### 5. `patient_dashboard.py`
**Purpose**: Patient-specific functionality
**Contents**:
- Medical record viewing
- Record sharing with doctors
- Data request management
- Patient-specific API calls

**Key Functions**:
- `render_patient_dashboard()`: Main patient dashboard
- `fetch_patient_records()`: Get patient records
- `fetch_data_requests()`: Get data requests for patient

### 6. `doctor_dashboard.py`
**Purpose**: Doctor-specific functionality
**Contents**:
- Medical record creation
- Shared record access
- Doctor-specific API calls

**Key Functions**:
- `render_doctor_dashboard()`: Main doctor dashboard

### 7. `hospital_dashboard.py`
**Purpose**: Hospital-specific functionality
**Contents**:
- Data purchase request management
- Group membership management
- Hospital-specific API calls

**Key Functions**:
- `render_hospital_dashboard()`: Main hospital dashboard
- `fetch_filled_templates()`: Get filled templates

### 8. `buyer_dashboard.py`
**Purpose**: Buyer-specific functionality
**Contents**:
- Data request creation
- Request status tracking
- Buyer-specific API calls

**Key Functions**:
- `render_buyer_dashboard()`: Main buyer dashboard

### 9. `manager_dashboard.py`
**Purpose**: Manager-specific functionality
**Contents**:
- Group manager dashboard
- Revocation manager dashboard
- Signature opening requests

**Key Functions**:
- `render_group_manager_dashboard()`: Group manager interface
- `render_revocation_manager_dashboard()`: Revocation manager interface

### 10. `main_refactored.py`
**Purpose**: Main application entry point
**Contents**:
- Application initialization
- Role-based routing
- Main application flow

**Key Features**:
- Modular imports
- Role-based dashboard rendering
- Welcome screen for unconnected users

## Benefits of Refactoring

### 1. **Maintainability**
- Each module has a single responsibility
- Easier to locate and fix bugs
- Clearer code organization

### 2. **Reusability**
- Common functions can be imported across modules
- Gas fee functionality is shared across all dashboards
- Configuration is centralized

### 3. **Testability**
- Individual modules can be tested in isolation
- Easier to write unit tests
- Better separation of concerns

### 4. **Scalability**
- New features can be added to specific modules
- Easier to add new roles or dashboards
- Reduced merge conflicts in team development

### 5. **Readability**
- Smaller, focused files are easier to understand
- Clear module boundaries
- Better documentation structure

## Migration Guide

### From Original `main.py` to Refactored Structure

1. **Replace the main file**:
   ```bash
   # Backup original
   cp app/main.py app/main_original.py
   
   # Use refactored version
   cp app/main_refactored.py app/main.py
   ```

2. **Update imports** (if needed):
   - All imports are handled automatically in the refactored modules
   - No changes needed to existing code

3. **Test functionality**:
   - All original functionality is preserved
   - Same user interface and behavior
   - Improved performance due to better organization

## Usage

### Running the Application

```bash
# Navigate to the app directory
cd app

# Run the refactored application
streamlit run main_refactored.py

# Or replace the original and run normally
streamlit run main.py
```

### Development Workflow

1. **Adding new features**:
   - Identify the appropriate module for the feature
   - Add the functionality to that module
   - Import and use in `main_refactored.py` if needed

2. **Modifying existing features**:
   - Locate the relevant module
   - Make changes in the focused module
   - Test the specific functionality

3. **Adding new roles**:
   - Create a new dashboard module (e.g., `admin_dashboard.py`)
   - Add role handling in `main_refactored.py`
   - Update `wallet_connection.py` for role selection

## Dependencies

The refactored modules maintain the same dependencies as the original application:

- `streamlit`
- `requests`
- `web3`
- `pandas`
- `datetime`
- `time`
- `hashlib`
- `base64`
- `json`
- `os`
- `dotenv`

## Future Enhancements

With the modular structure, future enhancements become easier:

1. **API Layer**: Create dedicated API modules for each service
2. **Database Layer**: Add database interaction modules
3. **Testing**: Add comprehensive test suites for each module
4. **Documentation**: Generate API documentation from modules
5. **Plugins**: Create plugin system for additional features

## Conclusion

The refactored structure provides a solid foundation for continued development while maintaining all existing functionality. The modular approach makes the codebase more maintainable, testable, and scalable. 