import streamlit as st
import datetime
import time
import hashlib
from config import API_URL, TEST_ACCOUNTS
from blockchain_utils import track_transaction
from api_client import api_client

def render_doctor_dashboard():
    """Render the doctor dashboard"""
    st.title("Doctor Dashboard")
    
    # Tabs for different doctor actions
    tab1, tab2, tab3 = st.tabs(["Create Records", "View Shared Records", "Gas Fees"])
    
    with tab1:
        st.header("Create Medical Records")
        
        # Form for creating a new medical record
        with st.form("create_record_form"):
            st.subheader("Patient Information")
            patient_address = st.text_input("Patient Wallet Address")
            
            st.subheader("Demographics")
            col1, col2 = st.columns(2)
            with col1:
                age = st.number_input("Age", min_value=0, max_value=150, value=30)
                gender = st.selectbox("Gender", ["Male", "Female", "Other", "Prefer not to say"])
            with col2:
                location = st.text_input("Location", "City, State")
                ethnicity = st.selectbox("Ethnicity", [
                    "White", "Black or African American", "Hispanic or Latino", 
                    "Asian", "Native American", "Pacific Islander", "Other", "Prefer not to say"
                ])
            
            st.subheader("Medical Information")
            diagnosis = st.text_area("Diagnosis")
            treatment = st.text_area("Treatment Plan")
            
            col1, col2 = st.columns(2)
            with col1:
                medications = st.text_area("Medications")
            with col2:
                lab_results = st.text_area("Lab Results")
            
            notes = st.text_area("Additional Notes")
            
            submit_button = st.form_submit_button("Create Record")
            
            if submit_button:
                if not patient_address:
                    st.error("Please enter a patient wallet address")
                elif not diagnosis:
                    st.error("Please enter a diagnosis")
                else:
                    # Prepare record data
                    record_data = {
                        "patient_address": patient_address,
                        "doctor_address": st.session_state.wallet_address,
                        "demographics": {
                            "age": age,
                            "gender": gender,
                            "location": location,
                            "ethnicity": ethnicity
                        },
                        "medical_data": {
                            "diagnosis": diagnosis,
                            "treatment": treatment,
                            "medications": medications,
                            "lab_results": lab_results
                        },
                        "notes": notes,
                        "timestamp": int(time.time())
                    }
                    try:
                        # Sign the record
                        sign_result = api_client.sign_record(record_data, st.session_state.wallet_address)
                        if sign_result and sign_result.get("success"):
                            signed_data = sign_result.get("data", {})
                            # Store the record
                            store_result = api_client.store_record(
                                record=signed_data.get("record", record_data),
                                signature=signed_data.get("signature", ""),
                                merkle_root=signed_data.get("merkleRoot", ""),
                                patient_address=patient_address,
                                hospital_info="General Hospital"
                            )
                            if store_result and store_result.get("success"):
                                result = store_result.get("data", {})
                                st.success("Medical record created and stored successfully!")
                                st.info(f"Record CID: {result.get('cid', 'N/A')}")
                                st.info(f"IPFS Link: https://ipfs.io/ipfs/{result.get('cid', 'N/A')}")
                                # Track the transaction
                                track_transaction(
                                    tx_hash=result.get('transaction_hash', ""),
                                    operation='Create Medical Record',
                                    wallet_address=st.session_state.wallet_address,
                                    gas_used=120000,  # Estimated gas used
                                    gas_price=None  # We don't know the gas price in this simulation
                                )
                                # Add to transaction history
                                if "transaction_history" not in st.session_state:
                                    st.session_state.transaction_history = []
                                new_tx = {
                                    "id": f"tx-{len(st.session_state.transaction_history) + 1:03d}",
                                    "type": "Create Record",
                                    "status": "Completed",
                                    "timestamp": int(time.time()),
                                    "tx_hash": result.get('transaction_hash', ""),
                                    "gas_fee": result.get('gas_fee', 0.0018),
                                    "amount": 0,
                                    "details": {
                                        "patient_address": patient_address,
                                        "diagnosis": diagnosis,
                                        "cid": result.get('cid', 'N/A')
                                    }
                                }
                                st.session_state.transaction_history.append(new_tx)
                                st.rerun()
                            else:
                                st.error(f"Error storing record: {store_result}")
                        else:
                            st.error(f"Error signing record: {sign_result}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")
    
    with tab2:
        st.header("View Shared Records")
        
        # Form for accessing shared records
        with st.form("access_shared_record_form"):
            sharing_metadata_cid = st.text_input("Sharing Metadata CID")
            
            submit_button = st.form_submit_button("Access Shared Record")
            
            if submit_button:
                if not sharing_metadata_cid:
                    st.error("Please enter a Sharing Metadata CID")
                else:
                    # Call API to access shared record
                    try:
                        response = requests.get(
                            f"{API_URL}/api/share/{sharing_metadata_cid}",
                            params={
                                "doctor_address": st.session_state.wallet_address
                            }
                        )
                        
                        # If the first URL fails, try the alternative URL
                        if response.status_code == 404:
                            print("Trying alternative API URL...")
                            response = requests.get(
                                f"{API_URL}/share/{sharing_metadata_cid}",
                                params={
                                    "doctor_address": st.session_state.wallet_address
                                }
                            )
                        
                        if response.status_code == 200:
                            result = response.json()
                            st.success("Shared record accessed successfully!")
                            
                            # Display the record
                            record = result.get('record', {})
                            if record:
                                st.subheader("Shared Medical Record")
                                
                                col1, col2 = st.columns(2)
                                with col1:
                                    st.markdown(f"**Patient:** {record.get('patient_address', 'N/A')}")
                                    st.markdown(f"**Doctor:** {record.get('doctor_address', 'N/A')}")
                                with col2:
                                    timestamp = record.get('timestamp', 0)
                                    if isinstance(timestamp, (int, float)):
                                        date_str = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                                        st.markdown(f"**Created:** {date_str}")
                                
                                # Demographics
                                st.subheader("Demographics")
                                demographics = record.get('demographics', {})
                                if demographics:
                                    col1, col2 = st.columns(2)
                                    with col1:
                                        st.markdown(f"**Age:** {demographics.get('age', 'N/A')}")
                                        st.markdown(f"**Gender:** {demographics.get('gender', 'N/A')}")
                                    with col2:
                                        st.markdown(f"**Location:** {demographics.get('location', 'N/A')}")
                                        st.markdown(f"**Ethnicity:** {demographics.get('ethnicity', 'N/A')}")
                                
                                # Medical data
                                st.subheader("Medical Data")
                                medical_data = record.get('medical_data', {})
                                if medical_data:
                                    st.markdown(f"**Diagnosis:** {medical_data.get('diagnosis', 'N/A')}")
                                    st.markdown(f"**Treatment:** {medical_data.get('treatment', 'N/A')}")
                                    
                                    col1, col2 = st.columns(2)
                                    with col1:
                                        st.markdown("**Medications:**")
                                        st.text_area("Medications", value=medical_data.get('medications', ''), height=100, disabled=True)
                                    with col2:
                                        st.markdown("**Lab Results:**")
                                        st.text_area("Lab Results", value=medical_data.get('lab_results', ''), height=100, disabled=True)
                                
                                # Notes
                                st.subheader("Additional Notes")
                                st.text_area("Notes", value=record.get('notes', ''), height=100, disabled=True)
                            else:
                                st.error("No record data found in the response")
                        else:
                            st.error(f"Error: {response.json().get('detail', 'Unknown error')}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")
    
    with tab3:
        # Use the reusable gas fees tab function
        from gas_fees import render_gas_fees_tab
        render_gas_fees_tab(st.session_state.wallet_address) 