import streamlit as st
import datetime
import time
import hashlib
from config import API_URL, TEST_ACCOUNTS
from blockchain_utils import track_transaction

def fetch_filled_templates():
    """Fetch filled templates for this hospital"""
    try:
        response = requests.get(
            f"{API_URL}/hospital/filled_templates",
            params={
                "hospital_address": st.session_state.wallet_address
            }
        )
        
        if response.status_code == 404:
            print("Trying alternative API URL...")
            response = requests.get(
                f"{API_URL}/api/hospital/filled_templates",
                params={
                    "hospital_address": st.session_state.wallet_address
                }
            )
        
        if response.status_code == 200:
            data = response.json()
            return data.get("filled_templates", [])
        else:
            print(f"Error fetching filled templates: {response.status_code}")
            return []
    except Exception as e:
        print(f"Error fetching filled templates: {str(e)}")
        return []

def render_hospital_dashboard():
    """Render the hospital dashboard"""
    st.title("Hospital Dashboard")
    
    # Auto-refresh toggle
    col1, col2 = st.columns([3, 1])
    with col1:
        st.subheader("Data Purchase Requests")
    with col2:
        auto_refresh = st.checkbox("Auto-refresh", value=st.session_state.get("hospital_auto_refresh", False))
        st.session_state.hospital_auto_refresh = auto_refresh
    
    # Tabs for different hospital actions
    tab1, tab2, tab3 = st.tabs(["Purchase Requests", "Group Management", "Gas Fees"])
    
    with tab1:
        # Initialize purchase requests in session state
        if "purchase_requests" not in st.session_state:
            st.session_state.purchase_requests = [
                {
                    "request_id": "req_001",
                    "buyer": "0x3Fa2c09c14453c7acaC39E3fd57e0c6F1da3f5ce",
                    "amount": 0.1,
                    "template": {
                        "category": "Cardiology",
                        "demographics": {"age": True, "gender": True},
                        "medical_data": {"diagnosis": True, "treatment": True},
                        "time_period": "1 year",
                        "min_records": 10
                    },
                    "status": "pending",
                    "timestamp": int(time.time()) - 3600,
                    "filled_records": 0,
                    "total_records": 15
                }
            ]
        
        # Display purchase requests
        if not st.session_state.purchase_requests:
            st.info("No purchase requests found.")
        else:
            for i, request in enumerate(st.session_state.purchase_requests):
                if request.get("status") == "pending":
                    with st.expander(f"Request {request['request_id']} - {request['template']['category']}"):
                        # Request details
                        col1, col2 = st.columns(2)
                        with col1:
                            st.markdown(f"**Request ID:** {request['request_id']}")
                            
                            # Format buyer address
                            buyer = request.get("buyer", "Unknown")
                            buyer_display = buyer
                            if len(buyer) > 10:
                                buyer_display = f"{buyer[:6]}...{buyer[-4:]}"
                            
                            # Check if it's a known address
                            buyer_name = "Unknown"
                            for name, info in TEST_ACCOUNTS.items():
                                if info["address"].lower() == buyer.lower():
                                    buyer_name = name
                                    break
                            
                            st.markdown(f"**Buyer:** {buyer_name} ({buyer_display})")
                            st.markdown(f"**Amount:** {request.get('amount', 0)} ETH")
                        
                        with col2:
                            # Format timestamp
                            timestamp = request.get("timestamp", 0)
                            if isinstance(timestamp, (int, float)):
                                date_str = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                                st.markdown(f"**Requested:** {date_str}")
                            
                            st.markdown(f"**Status:** {request.get('status', 'Unknown')}")
                            st.markdown(f"**Records:** {request.get('filled_records', 0)}/{request.get('total_records', 0)}")
                        
                        # Template details
                        st.subheader("Data Request Template")
                        template = request.get("template", {})
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            st.markdown(f"**Category:** {template.get('category', 'N/A')}")
                            st.markdown(f"**Time Period:** {template.get('time_period', 'N/A')}")
                            st.markdown(f"**Min Records:** {template.get('min_records', 'N/A')}")
                        
                        with col2:
                            st.markdown("**Requested Data:**")
                            demographics = template.get("demographics", {})
                            if demographics:
                                st.markdown("- Demographics: " + ", ".join([k for k, v in demographics.items() if v]))
                            
                            medical_data = template.get("medical_data", {})
                            if medical_data:
                                st.markdown("- Medical Data: " + ", ".join([k for k, v in medical_data.items() if v]))
                        
                        # Action buttons
                        col1, col2 = st.columns(2)
                        with col1:
                            if st.button(f"Reply with Data", key=f"reply_{i}"):
                                st.session_state.current_request = request
                                st.session_state.show_reply_form = True
                                st.rerun()
                        
                        with col2:
                            if st.button(f"Reject Request", key=f"reject_{i}"):
                                st.error(f"Request {request['request_id']} rejected!")
                                request["status"] = "rejected"
                                st.rerun()
        
        # Reply form
        if st.session_state.get("show_reply_form", False) and st.session_state.get("current_request"):
            st.subheader("Reply with Data")
            current_request = st.session_state.current_request
            
            # Form for replying with data
            with st.form("reply_with_data_form"):
                st.markdown(f"**Request ID:** {current_request['request_id']}")
                st.markdown(f"**Category:** {current_request['template']['category']}")
                
                # Get filled templates for this request
                filled_templates = fetch_filled_templates()
                
                if filled_templates:
                    st.markdown("**Available Records:**")
                    selected_records = []
                    
                    for template in filled_templates:
                        if template.get("category") == current_request['template']['category']:
                            # Check if template matches the request criteria
                            matches = True
                            for field, required in current_request['template'].get('demographics', {}).items():
                                if required and not template.get('demographics', {}).get(field):
                                    matches = False
                                    break
                            
                            for field, required in current_request['template'].get('medical_data', {}).items():
                                if required and not template.get('medical_data', {}).get(field):
                                    matches = False
                                    break
                            
                            if matches:
                                selected = st.checkbox(
                                    f"Record {template.get('cid', 'N/A')} - {template.get('patient_address', 'N/A')[:6]}...",
                                    key=f"record_{template.get('cid', 'N/A')}"
                                )
                                if selected:
                                    selected_records.append(template.get('cid'))
                
                submit_button = st.form_submit_button("Submit Reply")
                
                if submit_button:
                    if not selected_records:
                        st.error("Please select at least one record")
                    else:
                        # Call API to reply with data
                        try:
                            response = requests.post(
                                f"{API_URL}/api/purchase/reply",
                                json={
                                    "request_id": current_request['request_id'],
                                    "record_cids": selected_records,
                                    "hospital_address": st.session_state.wallet_address
                                },
                                headers={"Content-Type": "application/json"}
                            )
                            
                            if response.status_code == 404:
                                print("Trying alternative API URL...")
                                response = requests.post(
                                    f"{API_URL}/purchase/reply",
                                    json={
                                        "request_id": current_request['request_id'],
                                        "record_cids": selected_records,
                                        "hospital_address": st.session_state.wallet_address
                                    },
                                    headers={"Content-Type": "application/json"}
                                )
                            
                            if response.status_code == 200:
                                result = response.json()
                                st.success("Reply submitted successfully!")
                                
                                # Extract transaction hash from response
                                tx_hash = result.get('transaction_hash', "")
                                # Track the transaction
                                track_transaction(
                                    tx_hash=result.get('transaction_hash', ""),
                                    operation=f'Reply to Purchase Request',
                                    wallet_address=st.session_state.wallet_address,
                                    gas_used=95000,  # Estimated gas used
                                    gas_price=None
                                )
                                
                                # Update request status
                                current_request["status"] = "replied"
                                current_request["filled_records"] = len(selected_records)
                                
                                # Clear form state
                                st.session_state.show_reply_form = False
                                st.session_state.current_request = None
                                st.rerun()
                            else:
                                st.error(f"Error: {response.json().get('detail', 'Unknown error')}")
                        except Exception as e:
                            st.error(f"Error: {str(e)}")
                else:
                    st.info("No matching records found for this request")
    
    with tab2:
        st.header("Group Management")
        
        # Form for managing doctor group membership
        with st.form("group_management_form"):
            st.subheader("Add Doctor to Group")
            doctor_address = st.text_input("Doctor Wallet Address")
            
            col1, col2 = st.columns(2)
            with col1:
                action = st.selectbox("Action", ["Add", "Remove"])
            with col2:
                group_id = st.text_input("Group ID", value="1")
            
            submit_button = st.form_submit_button(f"{action} Doctor")
            
            if submit_button:
                if not doctor_address:
                    st.error("Please enter a doctor wallet address")
                else:
                    # Call API to manage group membership
                    try:
                        endpoint = "add_member" if action == "Add" else "remove_member"
                        response = requests.post(
                            f"{API_URL}/api/group/{endpoint}",
                            json={
                                "group_id": int(group_id),
                                "doctor_address": doctor_address,
                                "hospital_address": st.session_state.wallet_address
                            },
                            headers={"Content-Type": "application/json"}
                        )
                        
                        if response.status_code == 404:
                            print("Trying alternative API URL...")
                            response = requests.post(
                                f"{API_URL}/group/{endpoint}",
                                json={
                                    "group_id": int(group_id),
                                    "doctor_address": doctor_address,
                                    "hospital_address": st.session_state.wallet_address
                                },
                                headers={"Content-Type": "application/json"}
                            )
                        
                        if response.status_code == 200:
                            result = response.json()
                            st.success(f"Doctor {action.lower()}ed successfully!")
                            
                            # Track the transaction
                            track_transaction(
                                tx_hash=result.get('transaction_hash', ""),
                                operation=f'{action.capitalize()} Doctor',
                                wallet_address=st.session_state.wallet_address,
                                gas_used=120000,  # Estimated gas used
                                gas_price=None  # We don't know the gas price in this simulation
                            )
                        else:
                            st.error(f"Error: {response.json().get('detail', 'Unknown error')}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")
    
    with tab3:
        # Use the reusable gas fees tab function
        from gas_fees import render_gas_fees_tab
        render_gas_fees_tab(st.session_state.wallet_address) 