import streamlit as st
import datetime
import time
from config import API_URL
from gas_fees import render_gas_fees_tab

def render_group_manager_dashboard():
    """Render the group manager dashboard"""
    st.title("Group Manager Dashboard")
    
    # Tabs for different actions
    tab1, tab2 = st.tabs(["Signature Opening Requests", "Gas Fees"])
    
    with tab1:
        st.header("Signature Opening Requests")
        
        # Form for processing opening requests
        with st.form("process_opening_form"):
            opening_id = st.text_input("Opening Request ID")
            signature_hash = st.text_input("Signature Hash")
            
            submit_button = st.form_submit_button("Compute Partial Opening (Off-Chain)")
            
            if submit_button:
                if not opening_id or not signature_hash:
                    st.error("Please enter both Opening ID and Signature Hash")
                else:
                    # Call API to compute partial opening
                    try:
                        response = api_client.post(
                            f"{API_URL}/opening/compute_partial",
                            json={
                                "opening_id": int(opening_id),
                                "signature_hash": signature_hash,
                                "manager_type": "group",
                                "wallet_address": st.session_state.wallet_address
                            }
                        )
                        
                        if response.status_code == 200:
                            st.success("Partial opening computed successfully!")
                            st.info("You can now approve the opening on-chain.")
                            st.session_state.partial_computed = True
                            st.session_state.current_opening_id = opening_id
                        else:
                            st.error(f"Error: {response.json().get('detail', 'Unknown error')}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")
        
        # Button for on-chain approval (outside the form)
        if hasattr(st.session_state, 'partial_computed') and st.session_state.partial_computed:
            st.subheader("Approve Opening")
            if st.button("Approve Opening (On-Chain)"):
                # In a real implementation, this would call the smart contract
                st.success(f"Opening {st.session_state.current_opening_id} approved on-chain!")
                # Clear the state
                del st.session_state.partial_computed
                del st.session_state.current_opening_id
    
    with tab2:
        # Use the reusable gas fees tab function
        render_gas_fees_tab(st.session_state.wallet_address)

def render_revocation_manager_dashboard():
    """Render the revocation manager dashboard"""
    st.title("Revocation Manager Dashboard")
    
    # Tabs for different actions
    tab1, tab2 = st.tabs(["Signature Opening Requests", "Gas Fees"])
    
    with tab1:
        st.header("Signature Opening Requests")
        
        # Form for processing opening requests
        with st.form("process_opening_form"):
            opening_id = st.text_input("Opening Request ID")
            signature_hash = st.text_input("Signature Hash")
            
            submit_button = st.form_submit_button("Compute Partial Opening (Off-Chain)")
            
            if submit_button:
                if not opening_id or not signature_hash:
                    st.error("Please enter both Opening ID and Signature Hash")
                else:
                    # Call API to compute partial opening
                    try:
                        response = api_client.post(
                            f"{API_URL}/opening/compute_partial",
                            json={
                                "opening_id": int(opening_id),
                                "signature_hash": signature_hash,
                                "manager_type": "revocation",
                                "wallet_address": st.session_state.wallet_address
                            }
                        )
                        
                        if response.status_code == 200:
                            st.success("Partial opening computed successfully!")
                            st.info("You can now approve the opening on-chain.")
                            st.session_state.partial_computed = True
                            st.session_state.current_opening_id = opening_id
                        else:
                            st.error(f"Error: {response.json().get('detail', 'Unknown error')}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")
        
        # Button for on-chain approval (outside the form)
        if hasattr(st.session_state, 'partial_computed') and st.session_state.partial_computed:
            st.subheader("Approve Opening")
            if st.button("Approve Opening (On-Chain)"):
                # In a real implementation, this would call the smart contract
                st.success(f"Opening {st.session_state.current_opening_id} approved on-chain!")
                # Clear the state
                del st.session_state.partial_computed
                del st.session_state.current_opening_id
    
    with tab2:
        # Use the reusable gas fees tab function
        render_gas_fees_tab(st.session_state.wallet_address) 