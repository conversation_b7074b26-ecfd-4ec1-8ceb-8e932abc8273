import streamlit as st
import time
import datetime

# Set page config first - this must be the first Streamlit command
st.set_page_config(
    page_title="Healthcare Data Sharing",
    page_icon="🏥",
    layout="wide"
)

# Import all the modular components
from config import PAGE_CONFIG
from wallet_connection import initialize_session_state, render_wallet_sidebar
from patient_dashboard import render_patient_dashboard
from doctor_dashboard import render_doctor_dashboard
from hospital_dashboard import render_hospital_dashboard
from buyer_dashboard import render_buyer_dashboard
from manager_dashboard import render_group_manager_dashboard, render_revocation_manager_dashboard

# Initialize session state
initialize_session_state()

# Render the wallet sidebar
render_wallet_sidebar()

# Main application logic
if st.session_state.wallet_connected:
    # Get the current role
    role = st.session_state.selected_role
    
    # Render the appropriate dashboard based on role
    if role == "Patient":
        render_patient_dashboard()
    elif role == "Doctor":
        render_doctor_dashboard()
    elif role == "Hospital":
        render_hospital_dashboard()
    elif role == "Buyer":
        render_buyer_dashboard()
    elif role == "Group Manager":
        render_group_manager_dashboard()
    elif role == "Revocation Manager":
        render_revocation_manager_dashboard()
    else:
        st.error(f"Unknown role: {role}")
else:
    # Show welcome message when not connected
    st.title("Welcome to Healthcare Data Sharing")
    st.markdown("""
    This application enables secure, decentralized sharing of medical data using blockchain technology.
    
    **Features:**
    - 🔐 **Secure Authentication**: Connect with your wallet to access role-specific features
    - 📋 **Medical Records**: Create, view, and share medical records securely
    - 💰 **Data Marketplace**: Buy and sell anonymized medical data
    - 🏥 **Hospital Management**: Manage data requests and doctor groups
    - 🔍 **Signature Tracing**: Advanced cryptographic features for privacy and compliance
    
    **Roles Available:**
    - **Patient**: Access and share your medical records
    - **Doctor**: Create and sign medical records for patients
    - **Hospital**: Manage data purchase requests and group membership
    - **Buyer**: Request to purchase anonymized medical data
    - **Group Manager**: Manage doctor group membership and signature tracing
    - **Revocation Manager**: Assist in signature tracing and revocation
    
    **Getting Started:**
    1. Select your role from the sidebar
    2. Connect your wallet
    3. Start using the platform!
    """)
    
    # Display some statistics or demo information
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Total Records", "1,234")
    with col2:
        st.metric("Active Users", "567")
    with col3:
        st.metric("Data Requests", "89")

# Footer
st.markdown("---")
st.markdown("Decentralized Healthcare Data Sharing on BASE Testnet")

# Check if we need to rerun the app
if st.session_state.get("trigger_rerun", False):
    # Clear the trigger
    st.session_state.trigger_rerun = False
    # Rerun the app
    st.rerun()

# Check if auto-refresh is enabled for Hospital dashboard
elif st.session_state.get("hospital_auto_refresh", False):
    # Check if it's time for auto-refresh (every 30 seconds)
    current_time = int(time.time())
    last_refresh = st.session_state.get("hospital_last_refresh", 0)
    if (current_time - last_refresh) > 30:
        st.session_state.hospital_last_refresh = current_time
        st.rerun() 