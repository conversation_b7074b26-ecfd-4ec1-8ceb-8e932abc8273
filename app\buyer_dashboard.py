import streamlit as st
import datetime
import time
import hashlib
from config import API_URL, TEST_ACCOUNTS
from blockchain_utils import track_transaction

def render_buyer_dashboard():
    """Render the buyer dashboard"""
    st.title("Buyer Dashboard")
    
    # Tabs for different buyer actions
    tab1, tab2, tab3 = st.tabs(["Request Data", "View Requests", "Gas Fees"])
    
    with tab1:
        st.header("Request Medical Data")
        
        # Form for creating a data request
        with st.form("data_request_form"):
            st.subheader("Request Details")
            amount = st.number_input("Amount (ETH)", min_value=0.01, max_value=10.0, value=0.1, step=0.01)
            
            st.subheader("Data Template")
            category = st.selectbox("Medical Category", [
                "Cardiology", "Oncology", "Neurology", "Orthopedics", 
                "Pediatrics", "Psychiatry", "Dermatology", "General"
            ])
            
            time_period = st.selectbox("Time Period", [
                "6 months", "1 year", "2 years", "5 years", "All time"
            ])
            
            min_records = st.number_input("Minimum Records", min_value=1, max_value=1000, value=10)
            
            st.subheader("Requested Data Fields")
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**Demographics:**")
                age = st.checkbox("Age", value=True)
                gender = st.checkbox("Gender", value=True)
                location = st.checkbox("Location", value=False)
                ethnicity = st.checkbox("Ethnicity", value=False)
            
            with col2:
                st.markdown("**Medical Data:**")
                diagnosis = st.checkbox("Diagnosis", value=True)
                treatment = st.checkbox("Treatment", value=True)
                medications = st.checkbox("Medications", value=False)
                lab_results = st.checkbox("Lab Results", value=False)
            
            submit_button = st.form_submit_button("Submit Request")
            
            if submit_button:
                # Prepare request data
                request_data = {
                    "buyer_address": st.session_state.wallet_address,
                    "amount": amount,
                    "template": {
                        "category": category,
                        "demographics": {
                            "age": age,
                            "gender": gender,
                            "location": location,
                            "ethnicity": ethnicity
                        },
                        "medical_data": {
                            "diagnosis": diagnosis,
                            "treatment": treatment,
                            "medications": medications,
                            "lab_results": lab_results
                        },
                        "time_period": time_period,
                        "min_records": min_records
                    }
                }
                
                # Call API to create request
                try:
                    response = api_client.post(
                        f"{API_URL}/api/purchase/request",
                        json=request_data,
                        headers={"Content-Type": "application/json"}
                    )
                    
                    if response.status_code == 404:
                        print("Trying alternative API URL...")
                        response = api_client.post(
                            f"{API_URL}/purchase/request",
                            json=request_data,
                            headers={"Content-Type": "application/json"}
                        )
                    
                    if response.status_code == 200:
                        result = response.json()
                        st.success("Data request submitted successfully!")
                        st.info(f"Request ID: {result.get('request_id', 'N/A')}")
                        st.info(f"Transaction Hash: {result.get('transaction_hash', 'N/A')}")
                        
                        # Track the transaction
                        track_transaction(
                            tx_hash=result.get('transaction_hash', ""),
                            operation='Purchase Request',
                            wallet_address=st.session_state.wallet_address,
                            gas_used=120000,  # Estimated gas used
                            gas_price=None  # We don't know the gas price in this simulation
                        )
                        
                        # Add to transaction history
                        if "transaction_history" not in st.session_state:
                            st.session_state.transaction_history = []
                        
                        new_tx = {
                            "id": f"tx-{len(st.session_state.transaction_history) + 1:03d}",
                            "type": "Purchase Request",
                            "status": "Completed",
                            "timestamp": int(time.time()),
                            "tx_hash": result.get('transaction_hash', ""),
                            "gas_fee": result.get('gas_fee', 0.0018),
                            "amount": 0,
                            "details": {
                                "category": category,
                                "request_id": result.get('request_id', 'N/A')
                            }
                        }
                        st.session_state.transaction_history.append(new_tx)
                        
                        st.rerun()
                    else:
                        st.error(f"Error: {response.json().get('detail', 'Unknown error')}")
                except Exception as e:
                    st.error(f"Error: {str(e)}")
    
    with tab2:
        st.header("View Data Requests")
        
        # Initialize requests in session state
        if "buyer_requests" not in st.session_state:
            st.session_state.buyer_requests = [
                {
                    "request_id": "req_001",
                    "amount": 0.1,
                    "template": {
                        "category": "Cardiology",
                        "demographics": {"age": True, "gender": True},
                        "medical_data": {"diagnosis": True, "treatment": True},
                        "time_period": "1 year",
                        "min_records": 10
                    },
                    "status": "pending",
                    "timestamp": int(time.time()) - 3600,
                    "replies": []
                }
            ]
        
        # Display requests
        if not st.session_state.buyer_requests:
            st.info("No data requests found. Create a request to get started.")
        else:
            for i, request in enumerate(st.session_state.buyer_requests):
                with st.expander(f"Request {request['request_id']} - {request['template']['category']}"):
                    # Request details
                    col1, col2 = st.columns(2)
                    with col1:
                        st.markdown(f"**Request ID:** {request['request_id']}")
                        st.markdown(f"**Amount:** {request.get('amount', 0)} ETH")
                        st.markdown(f"**Status:** {request.get('status', 'Unknown')}")
                    
                    with col2:
                        # Format timestamp
                        timestamp = request.get("timestamp", 0)
                        if isinstance(timestamp, (int, float)):
                            date_str = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                            st.markdown(f"**Requested:** {date_str}")
                    
                    # Template details
                    st.subheader("Request Template")
                    template = request.get("template", {})
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.markdown(f"**Category:** {template.get('category', 'N/A')}")
                        st.markdown(f"**Time Period:** {template.get('time_period', 'N/A')}")
                        st.markdown(f"**Min Records:** {template.get('min_records', 'N/A')}")
                    
                    with col2:
                        st.markdown("**Requested Data:**")
                        demographics = template.get("demographics", {})
                        if demographics:
                            st.markdown("- Demographics: " + ", ".join([k for k, v in demographics.items() if v]))
                        
                        medical_data = template.get("medical_data", {})
                        if medical_data:
                            st.markdown("- Medical Data: " + ", ".join([k for k, v in medical_data.items() if v]))
                    
                    # Replies section
                    replies = request.get("replies", [])
                    if replies:
                        st.subheader("Hospital Replies")
                        for j, reply in enumerate(replies):
                            with st.expander(f"Reply {j+1} from {reply.get('hospital_address', 'Unknown')[:6]}..."):
                                st.markdown(f"**Hospital:** {reply.get('hospital_address', 'N/A')}")
                                st.markdown(f"**Records Provided:** {len(reply.get('record_cids', []))}")
                                st.markdown(f"**Timestamp:** {datetime.datetime.fromtimestamp(reply.get('timestamp', 0)).strftime('%Y-%m-%d %H:%M:%S')}")
                                
                                # Action buttons for finalizing
                                if request.get("status") == "replied":
                                    col1, col2 = st.columns(2)
                                    with col1:
                                        if st.button(f"Approve Reply", key=f"approve_reply_{i}_{j}"):
                                            st.success(f"Reply {j+1} approved!")
                                            # In a real implementation, this would call the smart contract
                                            request["status"] = "approved"
                                            st.rerun()
                                    with col2:
                                        if st.button(f"Reject Reply", key=f"reject_reply_{i}_{j}"):
                                            st.error(f"Reply {j+1} rejected!")
                                            # In a real implementation, this would call the smart contract
                                            request["status"] = "rejected"
                                            st.rerun()
                    else:
                        st.info("No replies received yet. Hospitals will respond here when they have matching data.")
    
    with tab3:
        # Use the reusable gas fees tab function
        from gas_fees import render_gas_fees_tab
        render_gas_fees_tab(st.session_state.wallet_address) 